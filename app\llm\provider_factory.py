from typing import Dict, Any, Optional
from app.llm.base import <PERSON><PERSON><PERSON>ider, LLMProviderFactory
from app.llm.openai_provider import create_openai_provider
from app.llm.deepseek_provider import create_deepseek_provider
from app.core.config import settings
from app.core.exceptions import LLMError


def create_llm_provider(
    provider_name: Optional[str] = None,
    config: Optional[Dict[str, Any]] = None
) -> LLMProvider:
    """
    Create an LLM provider based on configuration.
    
    Args:
        provider_name: Optional provider name override
        config: Optional configuration dictionary
        
    Returns:
        LLMProvider: Configured LLM provider instance
        
    Raises:
        LLMError: If provider creation fails
    """
    # Use provided provider name or default from settings
    provider = provider_name or settings.llm_provider
    
    # Default configuration if none provided
    if config is None:
        config = {}
    
    try:
        if provider == "deepseek":
            # Create DeepSeek provider
            deepseek_config = {
                "model": settings.deepseek_model,
                "api_key": settings.deepseek_api_key,
                "temperature": 0.7,
                "max_tokens": 2000,
                **config  # Override with any provided config
            }
            return create_deepseek_provider(deepseek_config)
            
        elif provider == "openai":
            # Create OpenAI provider
            openai_config = {
                "model": settings.openai_model,
                "api_key": settings.openai_api_key,
                "temperature": 0.7,
                "max_tokens": 2000,
                **config  # Override with any provided config
            }
            return create_openai_provider(openai_config)
            
        else:
            # Try to use the factory registry
            try:
                return LLMProviderFactory.create_provider(provider, config)
            except ValueError:
                raise LLMError(f"Unknown LLM provider: {provider}")
                
    except Exception as e:
        if isinstance(e, LLMError):
            raise e
        raise LLMError(f"Failed to create {provider} provider: {str(e)}")


def get_default_provider_config(provider_name: Optional[str] = None) -> Dict[str, Any]:
    """
    Get default configuration for a provider.
    
    Args:
        provider_name: Optional provider name
        
    Returns:
        Dict[str, Any]: Default configuration
    """
    provider = provider_name or settings.llm_provider
    
    if provider == "deepseek":
        return {
            "model": settings.deepseek_model,
            "api_key": settings.deepseek_api_key,
            "temperature": 0.7,
            "max_tokens": 2000
        }
    elif provider == "openai":
        return {
            "model": settings.openai_model,
            "api_key": settings.openai_api_key,
            "temperature": 0.7,
            "max_tokens": 2000
        }
    else:
        return {}


def get_current_provider_info() -> Dict[str, Any]:
    """
    Get information about the current default provider.
    
    Returns:
        Dict[str, Any]: Provider information
    """
    provider = settings.llm_provider
    config = get_default_provider_config(provider)
    
    return {
        "provider": provider,
        "model": config.get("model", "unknown"),
        "api_key_configured": bool(config.get("api_key"))
    }
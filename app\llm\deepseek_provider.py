import asyncio
import logging
from typing import List, Dict, Any, AsyncGenerator, Optional
from openai import AsyncOpenA<PERSON>

from app.llm.base import (
    LLMProvider,
    ChatMessage,
    LLMResponse,
    StreamingChunk,
    LLMProviderFactory
)
from app.core.config import settings
from app.core.exceptions import LLMError

logger = logging.getLogger(__name__)


class DeepSeekProvider(LLMProvider):
    """DeepSeek LLM provider implementation using OpenAI-compatible API."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # Initialize DeepSeek client (uses OpenAI-compatible API)
        api_key = config.get("api_key") or getattr(settings, 'deepseek_api_key', None)
        if not api_key:
            raise LLMError("DeepSeek API key is required")
        
        # DeepSeek API base URL
        base_url = config.get("base_url", "https://api.deepseek.com")
        
        # Configure timeout (default 60 seconds)
        timeout = config.get("timeout", 60.0)
        
        self.client = AsyncOpenAI(
            api_key=api_key,
            base_url=base_url,
            timeout=timeout
        )
        self.model = config.get("model", getattr(settings, 'deepseek_model', 'deepseek-chat'))
        self.default_temperature = config.get("temperature", 0.7)
        self.default_max_tokens = config.get("max_tokens", 2000)
    
    def _convert_messages(self, messages: List[ChatMessage]) -> List[Dict[str, str]]:
        """Convert internal message format to DeepSeek format."""
        deepseek_messages = []
        for msg in messages:
            deepseek_messages.append({
                "role": msg.role,
                "content": msg.content
            })
        return deepseek_messages
    
    async def generate_response(
        self,
        messages: List[ChatMessage],
        **kwargs
    ) -> LLMResponse:
        """
        Generate a complete response from DeepSeek.
        
        Args:
            messages: List of chat messages
            **kwargs: Additional parameters (temperature, max_tokens, etc.)
            
        Returns:
            LLMResponse: Complete response from DeepSeek
            
        Raises:
            LLMError: If the API call fails
        """
        try:
            # Prepare parameters
            deepseek_messages = self._convert_messages(messages)
            
            # Use provided parameters or defaults
            temperature = kwargs.get("temperature", self.default_temperature)
            max_tokens = kwargs.get("max_tokens", self.default_max_tokens)
            
            # Make API call
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=deepseek_messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=False
            )
            
            # Extract response data
            if not response.choices or len(response.choices) == 0:
                raise LLMError("DeepSeek API returned no choices in response")
            
            choice = response.choices[0]
            if not choice.message:
                raise LLMError("DeepSeek API returned no message in choice")
            
            content = choice.message.content
            if content is None:
                content = ""  # Handle None content gracefully
            
            # Prepare usage information
            usage = None
            if hasattr(response, 'usage') and response.usage:
                usage = {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                }
            
            return LLMResponse(
                content=content,
                usage=usage,
                finish_reason=choice.finish_reason,
                metadata={
                    "model": self.model,
                    "provider": "deepseek"
                }
            )
            
        except Exception as e:
            logger.error(f"DeepSeek provider error: {str(e)}", exc_info=True)
            if "deepseek" in str(type(e)).lower() or "openai" in str(type(e)).lower():
                raise LLMError(f"DeepSeek API error: {str(e)}")
            raise LLMError(f"Unexpected error in DeepSeek provider: {str(e)}")
    
    async def generate_stream(
        self,
        messages: List[ChatMessage],
        **kwargs
    ) -> AsyncGenerator[StreamingChunk, None]:
        """
        Generate a streaming response from DeepSeek.
        
        Args:
            messages: List of chat messages
            **kwargs: Additional parameters
            
        Yields:
            StreamingChunk: Streaming response chunks
            
        Raises:
            LLMError: If the API call fails
        """
        try:
            # Prepare parameters
            deepseek_messages = self._convert_messages(messages)
            
            temperature = kwargs.get("temperature", self.default_temperature)
            max_tokens = kwargs.get("max_tokens", self.default_max_tokens)
            
            # Make streaming API call
            stream = await self.client.chat.completions.create(
                model=self.model,
                messages=deepseek_messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=True
            )
            
            # Process streaming chunks
            async for chunk in stream:
                if chunk.choices and len(chunk.choices) > 0:
                    choice = chunk.choices[0]
                    
                    # Check if this is the final chunk
                    is_complete = choice.finish_reason is not None
                    
                    # Get content delta
                    content = ""
                    if choice.delta and choice.delta.content:
                        content = choice.delta.content
                    
                    # Prepare metadata
                    metadata = {
                        "model": self.model,
                        "provider": "deepseek"
                    }
                    
                    if is_complete and choice.finish_reason:
                        metadata["finish_reason"] = choice.finish_reason
                    
                    yield StreamingChunk(
                        content=content,
                        is_complete=is_complete,
                        metadata=metadata
                    )
                    
                    if is_complete:
                        break
                        
        except Exception as e:
            if "deepseek" in str(type(e)).lower() or "openai" in str(type(e)).lower():
                raise LLMError(f"DeepSeek API error: {str(e)}")
            raise LLMError(f"Unexpected error in DeepSeek streaming: {str(e)}")
    
    async def validate_connection(self) -> bool:
        """
        Validate DeepSeek connection by making a simple API call.
        
        Returns:
            bool: True if connection is valid
        """
        try:
            # Make a minimal API call to test connection
            test_messages = [ChatMessage(role="user", content="Test")]
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=self._convert_messages(test_messages),
                max_tokens=5,  # Minimal tokens for validation
                temperature=0
            )
            
            # Check if we got a valid response
            if response.choices and len(response.choices) > 0 and response.choices[0].message:
                return True
            return False
            
        except Exception as e:
            logger.debug(f"Connection validation failed: {str(e)}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get DeepSeek model information."""
        info = super().get_model_info()
        info.update({
            "api_base": str(self.client.base_url),
            "default_temperature": self.default_temperature,
            "default_max_tokens": self.default_max_tokens
        })
        return info


# Register the DeepSeek provider
LLMProviderFactory.register_provider("deepseek", DeepSeekProvider)


def create_deepseek_provider(config: Optional[Dict[str, Any]] = None) -> DeepSeekProvider:
    """
    Convenience function to create a DeepSeek provider.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        DeepSeekProvider: Configured DeepSeek provider instance
    """
    if config is None:
        config = {
            "model": getattr(settings, 'deepseek_model', 'deepseek-chat'),
            "api_key": getattr(settings, 'deepseek_api_key', None),
            "temperature": 0.7,
            "max_tokens": 2000
        }
    
    return DeepSeekProvider(config)
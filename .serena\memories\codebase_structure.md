# Codebase Structure

## Directory Layout
```
├── app/                        # Main application package
│   ├── main.py                # FastAPI application entry point
│   ├── api/                   # API endpoints
│   │   ├── auth.py            # Authentication endpoints
│   │   ├── chat.py            # Chat API endpoints
│   │   ├── session.py         # Session management endpoints
│   │   └── websocket.py       # WebSocket handlers
│   ├── core/                  # Core functionality
│   │   ├── config.py          # Configuration management (Pydantic settings)
│   │   ├── security.py        # Security utilities (JWT, password hashing)
│   │   ├── dependencies.py    # FastAPI dependencies
│   │   └── exceptions.py      # Custom exception classes
│   ├── models/                # SQLAlchemy ORM models
│   │   ├── base.py            # Database engine and base class
│   │   ├── user.py            # User model
│   │   ├── session.py         # Session model
│   │   └── message.py         # Message model
│   ├── schemas/               # Pydantic schemas for API validation
│   │   ├── auth.py            # Authentication schemas
│   │   ├── session.py         # Session schemas
│   │   └── chat.py            # Chat schemas
│   ├── agent/                 # AI agent system
│   │   ├── factory.py         # Agent factory and memory management
│   │   ├── role_loader.py     # Role configuration loader
│   │   └── tools/             # AI agent tools
│   ├── llm/                   # LLM provider abstraction
│   │   ├── base.py            # Base LLM provider interface
│   │   ├── openai_provider.py # OpenAI integration
│   │   ├── deepseek_provider.py # DeepSeek integration
│   │   └── provider_factory.py # Provider factory
│   ├── session/               # Session management
│   │   ├── manager.py         # Session business logic
│   │   └── storage.py         # Session storage (Redis)
│   └── utils/                 # Utilities
│       └── database.py        # Database utilities
├── roles/                     # AI role configurations
│   └── default_roles.yaml    # Default role definitions
├── config/                    # Configuration files
├── .doc/                      # Documentation
├── docker-compose.yml         # Docker services configuration
├── Dockerfile                 # Docker image configuration
├── requirements.txt           # Python dependencies
└── test_*.py                  # Test files
```

## Architecture Patterns
- **Layered Architecture**: API → Business Logic → Data Access
- **Dependency Injection**: FastAPI dependencies for database, auth, etc.
- **Factory Pattern**: Agent and LLM provider factories
- **Repository Pattern**: Session storage abstraction
- **Strategy Pattern**: Pluggable LLM providers
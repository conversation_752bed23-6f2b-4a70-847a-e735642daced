from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import logging

from app.core.config import settings
from app.core.exceptions import (
    AuthenticationError, 
    AuthorizationError, 
    ValidationError, 
    NotFoundError, 
    ConflictError,
    SessionError,
    LLMError,
    RateLimitError
)
from app.api import auth, session, chat, websocket

# Configure logging
logging.basicConfig(
    level=logging.INFO if not settings.debug else logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title=settings.app_name,
    description="Multi-User, Multi-Session, Multi-Role AI Agent System",
    version="1.0.0",
    debug=settings.debug
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Exception handlers
@app.exception_handler(AuthenticationError)
async def authentication_exception_handler(request: Request, exc: AuthenticationError):
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail, "type": "authentication_error"}
    )


@app.exception_handler(AuthorizationError)
async def authorization_exception_handler(request: Request, exc: AuthorizationError):
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail, "type": "authorization_error"}
    )


@app.exception_handler(ValidationError)
async def validation_exception_handler(request: Request, exc: ValidationError):
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail, "type": "validation_error"}
    )


@app.exception_handler(NotFoundError)
async def not_found_exception_handler(request: Request, exc: NotFoundError):
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail, "type": "not_found_error"}
    )


@app.exception_handler(ConflictError)
async def conflict_exception_handler(request: Request, exc: ConflictError):
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail, "type": "conflict_error"}
    )


@app.exception_handler(SessionError)
async def session_exception_handler(request: Request, exc: SessionError):
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail, "type": "session_error"}
    )


@app.exception_handler(LLMError)
async def llm_exception_handler(request: Request, exc: LLMError):
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail, "type": "llm_error"}
    )


@app.exception_handler(RateLimitError)
async def rate_limit_exception_handler(request: Request, exc: RateLimitError):
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail, "type": "rate_limit_error"}
    )


# Include routers
app.include_router(auth.router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(session.router, prefix="/api/v1/sessions", tags=["Sessions"])
app.include_router(chat.router, prefix="/api/v1/chat", tags=["Chat"])
app.include_router(websocket.router, prefix="/ws", tags=["WebSocket"])


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "app_name": settings.app_name,
        "version": "1.0.0",
        "environment": settings.app_env
    }


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "AI Agent System API",
        "version": "1.0.0",
        "docs_url": "/docs",
        "health_url": "/health"
    }


# Startup event
@app.on_event("startup")
async def startup_event():
    """Startup event handler."""
    logger.info(f"Starting {settings.app_name} in {settings.app_env} mode")
    
    # Initialize database tables (if needed)
    # This should be handled by Alembic migrations in production
    if settings.app_env == "development":
        try:
            from app.utils.database import create_tables
            create_tables()
            logger.info("Development mode: Database tables created")
        except Exception as e:
            logger.error(f"Failed to create database tables: {str(e)}")
    
    # Initialize LLM providers
    try:
        # Import providers to register them
        from app.llm import openai_provider, deepseek_provider
        from app.llm.provider_factory import get_current_provider_info
        
        provider_info = get_current_provider_info()
        logger.info(f"Default LLM provider: {provider_info['provider']} (model: {provider_info['model']})")
        logger.info(f"API key configured: {provider_info['api_key_configured']}")
    except Exception as e:
        logger.error(f"Failed to initialize LLM providers: {str(e)}")
    
    # Load and validate roles
    try:
        from app.agent.role_loader import role_loader
        active_roles = role_loader.list_role_names()
        logger.info(f"Loaded {len(active_roles)} active roles: {', '.join(active_roles)}")
    except Exception as e:
        logger.error(f"Failed to load roles: {str(e)}")


# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    """Shutdown event handler."""
    logger.info(f"Shutting down {settings.app_name}")


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level="debug" if settings.debug else "info"
    )
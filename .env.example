# Application
APP_NAME=AI_Agent_System
APP_ENV=development
DEBUG=true

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/agent_db
REDIS_URL=redis://localhost:6379/0

# LLM Provider Configuration
LLM_PROVIDER=deepseek

# DeepSeek (Default Provider)
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_MODEL=deepseek-chat

# OpenAI (Alternative Provider)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4

# Security
SECRET_KEY=your-secret-key-here-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRE_HOURS=24

# Limits
MAX_SESSIONS_PER_USER=10
MAX_MESSAGES_PER_SESSION=1000
MAX_MESSAGE_LENGTH=4000
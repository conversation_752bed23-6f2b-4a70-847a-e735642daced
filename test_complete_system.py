#!/usr/bin/env python3
"""
Comprehensive test script for the AI Agent System.
Tests all phases: Authentication, Session Management, LLM Integration, and WebSocket Chat.
"""

import requests
import json
import sys
import asyncio
import websockets
import time
from typing import Optional

BASE_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000"


class CompleteSystemTester:
    def __init__(self, base_url: str = BASE_URL, ws_url: str = WS_URL):
        self.base_url = base_url
        self.ws_url = ws_url
        self.token: Optional[str] = None
        self.session = requests.Session()
        self.current_session_key: Optional[str] = None
    
    def test_health(self) -> bool:
        """Test health endpoint."""
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                print("✅ Health check passed")
                return True
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Health check error: {str(e)}")
            return False
    
    def test_authentication(self) -> bool:
        """Test user registration and login."""
        print("\n📋 Testing Authentication")
        
        # Register user
        try:
            data = {
                "username": "testuser",
                "email": "<EMAIL>", 
                "password": "testpassword123"
            }
            response = self.session.post(f"{self.base_url}/api/v1/auth/register", json=data)
            
            if response.status_code in [201, 409]:  # Created or already exists
                print("✅ User registration successful")
            else:
                print(f"❌ User registration failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ User registration error: {str(e)}")
            return False
        
        # Login user
        try:
            data = {"username": "testuser", "password": "testpassword123"}
            response = self.session.post(
                f"{self.base_url}/api/v1/auth/login",
                data=data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            if response.status_code == 200:
                token_data = response.json()
                self.token = token_data["access_token"]
                self.session.headers.update({"Authorization": f"Bearer {self.token}"})
                print("✅ User login successful")
                return True
            else:
                print(f"❌ User login failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ User login error: {str(e)}")
            return False
    
    def test_session_management(self) -> bool:
        """Test session management."""
        print("\n📋 Testing Session Management")
        
        # List available roles
        try:
            response = self.session.get(f"{self.base_url}/api/v1/sessions/roles/")
            if response.status_code == 200:
                roles = response.json()
                print(f"✅ Found {len(roles['roles'])} available roles")
            else:
                print(f"❌ List roles failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ List roles error: {str(e)}")
            return False
        
        # Create session with unique name
        try:
            import time
            unique_name = f"Test AI Chat {int(time.time())}"
            data = {"name": unique_name, "role_name": "assistant"}
            response = self.session.post(f"{self.base_url}/api/v1/sessions/", json=data)
            
            if response.status_code == 201:
                session_data = response.json()
                self.current_session_key = session_data["session_key"]
                print(f"✅ Session created: {self.current_session_key}")
            else:
                print(f"❌ Session creation failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Session creation error: {str(e)}")
            return False
        
        # List sessions
        try:
            response = self.session.get(f"{self.base_url}/api/v1/sessions/")
            if response.status_code == 200:
                sessions_data = response.json()
                print(f"✅ Listed {sessions_data['total']} sessions")
            else:
                print(f"❌ List sessions failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ List sessions error: {str(e)}")
            return False
        
        return True
    
    def test_chat_api(self) -> bool:
        """Test REST chat API."""
        print("\n📋 Testing Chat API")
        
        if not self.current_session_key:
            print("❌ No session available for chat test")
            return False
        
        try:
            # Send a chat message
            data = {
                "content": "Hello! Can you help me with a simple math problem: what is 2 + 2?",
                "session_key": self.current_session_key
            }
            response = self.session.post(f"{self.base_url}/api/v1/chat/", json=data)
            
            if response.status_code == 200:
                chat_response = response.json()
                print(f"✅ Chat response received: {chat_response['content'][:100]}...")
                print(f"   Session: {chat_response['session_key']}")
                print(f"   Role: {chat_response['role_name']}")
            else:
                print(f"❌ Chat failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Chat error: {str(e)}")
            return False
        
        # Test message history
        try:
            response = self.session.get(f"{self.base_url}/api/v1/chat/{self.current_session_key}/history")
            if response.status_code == 200:
                history = response.json()
                print(f"✅ Message history retrieved: {history['total']} messages")
            else:
                print(f"❌ Message history failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Message history error: {str(e)}")
            return False
        
        return True
    
    async def test_websocket_chat(self) -> bool:
        """Test WebSocket streaming chat."""
        print("\n📋 Testing WebSocket Chat")
        
        if not self.current_session_key or not self.token:
            print("❌ No session or token available for WebSocket test")
            return False
        
        try:
            uri = f"{self.ws_url}/ws/chat?session_key={self.current_session_key}&token={self.token}"
            
            async with websockets.connect(uri) as websocket:
                print("✅ WebSocket connection established")
                
                # Wait for connection confirmation
                response = await websocket.recv()
                data = json.loads(response)
                if data.get("type") == "connection.established":
                    print("✅ WebSocket connection confirmed")
                else:
                    print(f"❌ Unexpected connection response: {data}")
                    return False
                
                # Send a chat message
                message = {
                    "type": "chat.message",
                    "content": "Can you solve this equation: x^2 + 5x + 6 = 0?"
                }
                await websocket.send(json.dumps(message))
                print("✅ Chat message sent via WebSocket")
                
                # Receive streaming response
                full_response = ""
                message_started = False
                
                while True:
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                        data = json.loads(response)
                        
                        if data.get("type") == "chat.message.start":
                            print("✅ Streaming response started")
                            message_started = True
                        elif data.get("type") == "chat.message.chunk":
                            content = data.get("content", "")
                            full_response += content
                            print(f"📥 Chunk: {content}", end="", flush=True)
                        elif data.get("type") == "chat.message.end":
                            print(f"\n✅ Streaming response completed")
                            print(f"   Full response: {full_response[:200]}...")
                            break
                        elif data.get("type") == "error":
                            print(f"❌ WebSocket error: {data.get('metadata', {}).get('error')}")
                            return False
                            
                    except asyncio.TimeoutError:
                        print("❌ WebSocket response timeout")
                        return False
                
                if not message_started or not full_response:
                    print("❌ No proper streaming response received")
                    return False
                
                return True
                
        except Exception as e:
            print(f"❌ WebSocket error: {str(e)}")
            return False
    
    def test_agent_info(self) -> bool:
        """Test agent information endpoint."""
        print("\n📋 Testing Agent Info")
        
        if not self.current_session_key:
            print("❌ No session available for agent info test")
            return False
        
        try:
            response = self.session.get(f"{self.base_url}/api/v1/chat/{self.current_session_key}/agent-info")
            if response.status_code == 200:
                agent_info = response.json()
                print(f"✅ Agent info retrieved:")
                print(f"   Role: {agent_info.get('role_name')}")
                print(f"   Tools: {agent_info.get('tools', [])}")
                print(f"   Messages: {agent_info.get('message_count', 0)}")
                return True
            else:
                print(f"❌ Agent info failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Agent info error: {str(e)}")
            return False
    
    async def run_all_tests(self):
        """Run all system tests."""
        print("🧪 Starting Complete System Tests\n")
        
        tests = [
            ("Health Check", self.test_health),
            ("Authentication", self.test_authentication),
            ("Session Management", self.test_session_management),
            ("Chat API", self.test_chat_api),
            ("Agent Info", self.test_agent_info),
        ]
        
        # Run synchronous tests
        for test_name, test_func in tests:
            if not test_func():
                print(f"\n❌ {test_name} failed. Stopping tests.")
                return False
        
        # Run WebSocket test
        print("\n📋 Testing WebSocket Chat")
        if not await self.test_websocket_chat():
            print("\n❌ WebSocket Chat failed.")
            return False
        
        print("\n✅ All tests passed! The complete AI Agent System is working correctly.")
        print("\n🎉 System Status:")
        print("   ✅ Phase 1: Authentication - Complete")
        print("   ✅ Phase 2: Session Management - Complete")
        print("   ✅ Phase 3: LLM Integration - Complete")
        print("   ✅ Phase 4: WebSocket Communication - Complete")
        print("   ✅ Phase 5: Tool Framework - Complete")
        
        print("\n📱 Ready for:")
        print("   - Multi-user concurrent access")
        print("   - Real-time streaming chat")
        print("   - Role-based AI conversations")
        print("   - Session persistence and management")
        print("   - Tool-enhanced AI capabilities")
        
        return True


async def main():
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
        ws_url = base_url.replace("http://", "ws://").replace("https://", "wss://")
    else:
        base_url = BASE_URL
        ws_url = WS_URL
    
    tester = CompleteSystemTester(base_url, ws_url)
    success = await tester.run_all_tests()
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
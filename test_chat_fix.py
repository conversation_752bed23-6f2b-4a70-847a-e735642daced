#!/usr/bin/env python3
"""
Test script to verify the chat endpoint fix.
"""
import asyncio
import httpx
import json
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_USER_DATA = {
    "username": "testuser",
    "email": "<EMAIL>", 
    "password": "testpassword123"
}

async def test_chat_endpoint():
    """Test the chat endpoint after the fixes."""
    async with httpx.AsyncClient(timeout=120.0) as client:
        try:
            print("🔍 Testing chat endpoint after fixes...")
            
            # Step 1: Health check
            print("1. Checking health endpoint...")
            response = await client.get(f"{BASE_URL}/health")
            if response.status_code == 200:
                print("   ✅ Health check passed")
            else:
                print(f"   ❌ Health check failed: {response.status_code}")
                return
            
            # Step 2: Login (assuming user exists)
            print("2. Attempting login...")
            login_response = await client.post(
                f"{BASE_URL}/api/v1/auth/login",
                data=TEST_USER_DATA
            )
            
            if login_response.status_code == 200:
                print("   ✅ Login successful")
                auth_data = login_response.json()
                token = auth_data.get("access_token")
                headers = {"Authorization": f"Bearer {token}"}
            else:
                print(f"   ❌ Login failed: {login_response.status_code}")
                print(f"   Response: {login_response.text}")
                return
            
            # Step 3: Test chat endpoint
            print("3. Testing chat endpoint...")
            chat_data = {
                "content": "Hello, this is a test message",
                "role_name": "assistant"
            }
            
            chat_response = await client.post(
                f"{BASE_URL}/api/v1/chat/",
                json=chat_data,
                headers=headers
            )
            
            print(f"   Response status: {chat_response.status_code}")
            
            if chat_response.status_code == 200 or chat_response.status_code == 201:
                print("   ✅ Chat endpoint working!")
                response_data = chat_response.json()
                print(f"   AI Response: {response_data.get('content', 'No content')[:100]}...")
                print(f"   Session Key: {response_data.get('session_key', 'None')}")
            else:
                print(f"   ❌ Chat endpoint failed: {chat_response.status_code}")
                try:
                    error_data = chat_response.json()
                    print(f"   Error: {error_data}")
                except:
                    print(f"   Error text: {chat_response.text}")
            
            # Step 4: Test with existing session
            if chat_response.status_code in [200, 201]:
                print("4. Testing with existing session...")
                response_data = chat_response.json()
                session_key = response_data.get('session_key')
                
                if session_key:
                    follow_up_data = {
                        "content": "Can you help me with a follow-up question?",
                        "session_key": session_key
                    }
                    
                    follow_up_response = await client.post(
                        f"{BASE_URL}/api/v1/chat/",
                        json=follow_up_data,
                        headers=headers
                    )
                    
                    if follow_up_response.status_code in [200, 201]:
                        print("   ✅ Follow-up message working!")
                        follow_up_data = follow_up_response.json()
                        print(f"   AI Response: {follow_up_data.get('content', 'No content')[:100]}...")
                    else:
                        print(f"   ❌ Follow-up failed: {follow_up_response.status_code}")
                        print(f"   Error: {follow_up_response.text}")
                        
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")

if __name__ == "__main__":
    print(f"🚀 Starting chat endpoint test at {datetime.now()}")
    asyncio.run(test_chat_endpoint())
    print(f"✅ Test completed at {datetime.now()}")